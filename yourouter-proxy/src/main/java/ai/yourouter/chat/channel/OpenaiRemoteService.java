package ai.yourouter.chat.channel;


import ai.yourouter.chat.channel.response.openai.ChatCompletion;
import ai.yourouter.chat.channel.response.openai.ChatCompletionChunk;
import ai.yourouter.chat.channel.utils.GoogleDomainUtils;
import ai.yourouter.chat.config.RetryConfig;
import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.chat.remote.response.BestKeyResponse;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.context.usage.ChatUsage;
import ai.yourouter.common.exception.CognitionWebException;
import ai.yourouter.common.exception.error.OpenAIError;
import ai.yourouter.common.utils.JsonUtils;
import ai.yourouter.common.utils.TraceUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Instant;
import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;

@Slf4j
@Service
@SuppressWarnings({"DuplicatedCode", "unused", "LoggingSimilarMessage"})
public class OpenaiRemoteService extends LlmRemoteService {

    // 常量定义
    private static final String OPENAI_CHAT_COMPLETIONS_PATH = "/v1/chat/completions";
    private static final String OPENAI_RESPONSES_PATH = "/v1/responses";
    private static final String GEMINI_MODEL_PREFIX = "gemini/";
    private static final String STREAM_DONE_MARKER = "[DONE]";
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String MODEL_FIELD = "model";
    private static final String PROJECT_ID_FIELD = "projectId";

    @Qualifier("httpWebClient")
    private final WebClient httpWebClient;

    @Qualifier("sseWebClient")
    private final WebClient sseWebClient;

    public OpenaiRemoteService(KmgRemoteService kmgRemoteService,
                               @Qualifier("httpWebClient") WebClient httpWebClient,
                               @Qualifier("sseWebClient") WebClient sseWebClient,
                               ObjectMapper objectMapper,
                               RetryConfig retryConfig) {
        super(kmgRemoteService);
        this.httpWebClient = httpWebClient;
        this.sseWebClient = sseWebClient;
    }

    /**
     * 构建API请求的域名URL
     *
     * @param chatContext 聊天上下文
     * @return 完整的API URL
     */
    private String buildApiUrl(ChatContext chatContext) {
        BestKeyResponse keyInfo = chatContext.getKeyInfo();
        if (keyInfo == null) {
            throw new CognitionWebException("Key info is null in chat context");
        }

        String domain = keyInfo.getDomain();
        if (chatContext.getChatModelInfo().onGemini()) {
            String projectId = keyInfo.getMetadata().get(PROJECT_ID_FIELD);
            if (projectId == null) {
                throw new CognitionWebException("Project ID is required for Gemini models");
            }
            return GoogleDomainUtils.buildGeminiOpenAIUrl(projectId);
        } else if (keyInfo.onAzure()) {
            return domain + "/openai/deployments/%s/chat/completions?api-version=%s".formatted(chatContext.apiModelName(), chatContext.getChatRequestStatistic().azureVersion());
        } else {
            if (domain.endsWith("v1") || domain.endsWith("v2") || domain.endsWith("v3") || domain.endsWith("v4") || domain.endsWith("v5") || domain.endsWith("v6")) {
                return domain + "/chat/completions";
            }
            return domain + OPENAI_CHAT_COMPLETIONS_PATH;
        }
    }

    /**
     * 构建Response API请求的域名URL
     *
     * @param chatContext 聊天上下文
     * @return 完整的Response API URL
     */
    private String buildResponseApiUrl(ChatContext chatContext) {
        BestKeyResponse keyInfo = chatContext.getKeyInfo();
        if (keyInfo == null) {
            throw new CognitionWebException("Key info is null in chat context");
        }


        String domain = keyInfo.getDomain();
        if (keyInfo.onAzure()) {
            String[] parts = domain.split("\\.");
            String subdomain = parts[0].replace("https://", "");
            return "https://%s.openai.azure.com/openai/v1/responses?api-version=preview".formatted(subdomain);
        }
        if (domain.endsWith("v1") || domain.endsWith("v2") || domain.endsWith("v3") || domain.endsWith("v4") || domain.endsWith("v5") || domain.endsWith("v6")) {
            return domain + "/responses";
        }
        return domain + OPENAI_RESPONSES_PATH;
    }

    /**
     * 准备请求体，为Gemini模型添加前缀
     *
     * @param body        原始请求体
     * @param chatContext 聊天上下文
     */
    private void prepareRequestBody(HashMap<String, Object> body, ChatContext chatContext) {
        if (chatContext.getChatModelInfo().onGemini()) {
            body.put(MODEL_FIELD, GEMINI_MODEL_PREFIX + chatContext.apiModelName());
        }
    }


    public Flux<ServerSentEvent<String>> streamOpenAiCompletion(HashMap<String, Object> body, ChatContext chatContext) {
        var isFirst = new AtomicBoolean(true);
        prepareRequestBody(body, chatContext);

        return Mono.just(chatContext.getKeyInfo())
                .flatMapMany(key -> createStreamRequest(body, chatContext, key, isFirst));
    }

    /**
     * 创建流式请求
     */
    private Flux<ServerSentEvent<String>> createStreamRequest(HashMap<String, Object> body,
                                                              ChatContext chatContext,
                                                              BestKeyResponse key,
                                                              AtomicBoolean isFirst) {
        String apiUrl = buildApiUrl(chatContext);

        var bodySpec = sseWebClient.post().uri(apiUrl);
        if (key.onAzure()) {
            bodySpec.header("api-key", key.getToken());
        } else {
            bodySpec.header(HttpHeaders.AUTHORIZATION, BEARER_PREFIX + key.getToken());
        }
        return bodySpec.bodyValue(body)
                .retrieve()
                .bodyToFlux(String.class)
                .map(processStream(chatContext, isFirst))
                .map(response -> ServerSentEvent.<String>builder().data(" " + response).build())
                .publishOn(Schedulers.boundedElastic())
                .doOnError(throwable -> handleWebClientError(throwable, key, chatContext))
                .doOnComplete(() -> handleApiResult(key, chatContext, true));
    }

    /**
     * 处理流式响应数据
     */
    private static Function<String, String> processStream(ChatContext chatContext, AtomicBoolean isFirst) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            // 处理流式结束标记
            if (Objects.equals(message.trim(), STREAM_DONE_MARKER)) {
                log.debug("流式请求完成 | 用户ID: {} | 模型: {}", userId, modelName);
                return message;
            }

            // 记录首次响应时间
            if (isFirst.getAndSet(false)) {
                chatContext.getChatRequestStatistic().setPreparationTime(Instant.now().toEpochMilli());
                log.debug("收到首个数据块 | 用户ID: {} | 模型: {}", userId, modelName);
            }

            try {
                // 解析响应数据
                ChatCompletionChunk chatCompletionChunk = JsonUtils.parseObject(message, ChatCompletionChunk.class);

                // 更新使用量统计
                updateUsageIfPresent(chatContext, chatCompletionChunk, userId, modelName);

                // 收集流式响应内容到 streamResponse
                if (!message.trim().isEmpty()) {
                    chatContext.getChatRequestStatistic().getStreamResponse().append(message).append("\n");
                }

                // 使用父类的共性方法添加vendor和ID信息
                return processStreamWithVendorAndId(message, chatContext);

            } catch (Exception e) {
                log.error("流式数据解析错误 | 用户ID: {} | 模型: {} | 错误: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException(JsonUtils.toJSONString(OpenAIError.serviceUnavailable()));
            }
        };
    }

    /**
     * 更新使用量统计（如果存在）
     */
    private static void updateUsageIfPresent(ChatContext chatContext,
                                             ChatCompletionChunk chunk,
                                             Long userId,
                                             String modelName) {
        if (chunk.getUsage() != null) {
            chatContext.setChatUsage(ChatUsage.create(chunk, false));
            log.info("使用量更新 | 类型: {} | 用户ID: {} | 模型: {} | 使用量: {}",
                    "流式",
                    userId,
                    modelName,
                    JsonUtils.toJSONString(chunk.getUsage()));
        }
    }

    public Mono<Object> nonStreamOpenAiCompletion(HashMap<String, Object> body, ChatContext chatContext) {
        prepareRequestBody(body, chatContext);

        return Mono.just(chatContext.getKeyInfo())
                .flatMap(key -> createNonStreamRequest(body, chatContext, key));
    }

    /**
     * 创建非流式请求
     */
    private Mono<Object> createNonStreamRequest(HashMap<String, Object> body,
                                                ChatContext chatContext,
                                                BestKeyResponse key) {
        String apiUrl = buildApiUrl(chatContext);
        var bodySpec = httpWebClient.post().uri(apiUrl);
        if (key.onAzure()) {
            bodySpec.header("api-key", key.getToken());
        } else {
            bodySpec.header(HttpHeaders.AUTHORIZATION, BEARER_PREFIX + key.getToken());
        }
        return bodySpec
                .bodyValue(body)
                .retrieve()
                .bodyToMono(String.class)
                .map(processNonStream(chatContext))
                .publishOn(Schedulers.boundedElastic())
                .doOnError(throwable -> handleWebClientError(throwable, key, chatContext))
                .doOnSuccess(result -> handleApiResult(key, chatContext, true));
    }

    /**
     * 处理非流式响应数据
     */
    private Function<String, Object> processNonStream(ChatContext chatContext) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            try {
                log.debug("接收非流式响应 | 用户ID: {} | 模型: {}", userId, modelName);

                // 解析响应数据
                ChatCompletion chatCompletion = JsonUtils.parseObject(message, ChatCompletion.class);

                // 更新使用量统计
                updateUsageIfPresent(chatContext, chatCompletion, userId, modelName);

                // 使用父类的共性方法添加vendor和ID信息
                return processNonStreamWithVendorAndId(message, chatContext);

            } catch (Exception e) {
                log.error("非流式响应解析错误 | 用户ID: {} | 模型: {} | 错误: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException(JsonUtils.toJSONString(OpenAIError.serviceUnavailable()));
            }
        };
    }

    /**
     * 更新使用量统计（ChatCompletion版本）
     */
    private static void updateUsageIfPresent(ChatContext chatContext,
                                             ChatCompletion completion,
                                             Long userId,
                                             String modelName) {
        if (completion.getUsage() != null) {
            chatContext.setChatUsage(ChatUsage.create(completion, false));
            log.info("使用量更新 | 类型: {} | 用户ID: {} | 模型: {} | 使用量: {}",
                    "非流式",
                    userId,
                    modelName,
                    JsonUtils.toJSONString(completion.getUsage()));
        }
    }

    /**
     * 非流式OpenAI Response API请求
     *
     * @param body        请求体
     * @param chatContext 聊天上下文
     * @return 响应结果
     */
    public Mono<Object> nonStreamOpenAiResponse(HashMap<String, Object> body, ChatContext chatContext) {
        prepareRequestBody(body, chatContext);

        return Mono.just(chatContext.getKeyInfo())
                .flatMap(key -> createNonStreamResponseRequest(body, chatContext, key));
    }

    /**
     * 创建非流式Response请求
     */
    private Mono<Object> createNonStreamResponseRequest(HashMap<String, Object> body,
                                                        ChatContext chatContext,
                                                        BestKeyResponse key) {
        String apiUrl = buildResponseApiUrl(chatContext);
        var bodySpec = httpWebClient.post().uri(apiUrl);

        if (key.onAzure()) {
            bodySpec.header("api-key", key.getToken());
        } else {
            bodySpec.header(HttpHeaders.AUTHORIZATION, BEARER_PREFIX + key.getToken());
        }

        return bodySpec
                .bodyValue(body)
                .retrieve()
                .bodyToMono(String.class)
                .map(processNonStreamResponse(chatContext))
                .publishOn(Schedulers.boundedElastic())
                .doOnError(throwable -> handleWebClientError(throwable, key, chatContext))
                .doOnSuccess(result -> handleApiResult(key, chatContext, true));
    }

    /**
     * 处理非流式Response响应数据
     */
    private Function<String, Object> processNonStreamResponse(ChatContext chatContext) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            try {
                // Log detailed response at DEBUG level to avoid excessive logging
                log.debug("接收非流式Response响应 | 用户ID: {} | 模型: {} | 消息内容: {}",
                        userId, modelName, message);

                var jsonNode = JsonUtils.parseJson(message);
                var usage = jsonNode.path("usage");

                if (!usage.isMissingNode()) {
                    // 解析使用量信息
                    var inputTokens = usage.path("input_tokens").asInt(0);
                    var outputTokens = usage.path("output_tokens").asInt(0);
                    var cachedTokens = usage.path("input_token_details").path("cached_tokens").asInt(0);
                    var reasoningTokens = usage.path("output_token_details").path("reasoning_tokens").asInt(0);

                    chatContext.setChatUsage(new ChatUsage(inputTokens, outputTokens, cachedTokens, reasoningTokens));
                    log.info("更新使用情况 | 请求类型: 非流式 | 用户ID: {} | 模型: {} | 使用量: input={}, output={}, cached={}, reasoning={}",
                            userId, modelName, inputTokens, outputTokens, cachedTokens, reasoningTokens);
                }

                // 使用父类的共性方法添加vendor和ID信息
                return processNonStreamWithVendorAndId(message, chatContext);

            } catch (Exception e) {
                log.error("响应解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}", userId, modelName, e.getMessage());
                throw new CognitionWebException(JsonUtils.toJSONString(OpenAIError.serviceUnavailable()));
            }
        };
    }

    /**
     * 流式OpenAI Response API请求
     *
     * @param body        请求体
     * @param chatContext 聊天上下文
     * @return 流式响应结果
     */
    public Flux<ServerSentEvent<String>> streamOpenAiResponse(HashMap<String, Object> body, ChatContext chatContext) {
        var isFirst = new AtomicBoolean(true);
        prepareRequestBody(body, chatContext);

        return Mono.just(chatContext.getKeyInfo())
                .flatMapMany(key -> createStreamResponseRequest(body, chatContext, key, isFirst));
    }

    /**
     * 创建流式Response请求
     */
    private Flux<ServerSentEvent<String>> createStreamResponseRequest(HashMap<String, Object> body,
                                                                      ChatContext chatContext,
                                                                      BestKeyResponse key,
                                                                      AtomicBoolean isFirst) {
        String apiUrl = buildResponseApiUrl(chatContext);
        var bodySpec = sseWebClient.post().uri(apiUrl);

        if (key.onAzure()) {
            bodySpec.header("api-key", key.getToken());
        } else {
            bodySpec.header(HttpHeaders.AUTHORIZATION, BEARER_PREFIX + key.getToken());
        }

        return bodySpec.bodyValue(body)
                .retrieve()
                .bodyToFlux(String.class)
                .map(processResponseStream(chatContext, isFirst))
                .map(response -> ServerSentEvent.<String>builder().data(" " + response._1).event(response._2).build())
                .publishOn(Schedulers.boundedElastic())
                .doOnError(throwable -> handleWebClientError(throwable, key, chatContext))
                .doOnComplete(() -> handleApiResult(key, chatContext, true));
    }

    /**
     * 处理流式Response响应数据
     */
    private Function<String, Tuple2<String, String>> processResponseStream(ChatContext chatContext, AtomicBoolean isFirst) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            if (isFirst.getAndSet(false)) {
                chatContext.getChatRequestStatistic().setPreparationTime(Instant.now().toEpochMilli());
                log.debug("收到首个数据块 | 用户ID: {} | 模型: {}", userId, modelName);
            }

            try {
                // Log detailed response at DEBUG level to avoid excessive logging
                log.debug("接收数据块 | 用户ID: {} | 模型: {} | 消息内容: {}", userId, modelName, message);

                var jsonNode = JsonUtils.parseJson(message);
                var type = jsonNode.path("type").asText();

                // 处理包含response字段的消息，修改ID并注入vendor信息
                String processedMessage = message;
                if (jsonNode.has("response")) {
                    processedMessage = processStreamResponseWithVendorAndId(message, chatContext);
                }

                if ("response.completed".equalsIgnoreCase(type)) {
                    var usage = jsonNode.path("response").path("usage");
                    if (!usage.isMissingNode()) {
                        var inputTokens = usage.path("input_tokens").asInt(0);
                        var outputTokens = usage.path("output_tokens").asInt(0);
                        var cachedTokens = usage.path("input_token_details").path("cached_tokens").asInt(0);
                        var reasoningTokens = usage.path("output_token_details").path("reasoning_tokens").asInt(0);

                        chatContext.setChatUsage(new ChatUsage(inputTokens, outputTokens, cachedTokens, reasoningTokens));
                        log.info("更新使用情况 | 请求类型: 流式 | 用户ID: {} | 模型: {} | 使用量: input={}, output={}, cached={}, reasoning={}",
                                userId, modelName, inputTokens, outputTokens, cachedTokens, reasoningTokens);
                    }
                }

                log.debug("数据块解析完成 | 用户ID: {} | 模型: {} | 响应内容: {}",
                        userId, modelName, processedMessage);
                return Tuple.of(processedMessage, type);

            } catch (Exception e) {
                log.error("数据块解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException(JsonUtils.toJSONString(OpenAIError.serviceUnavailable()));
            }
        };
    }

    /**
     * 处理包含response字段的流式响应，修改ID并注入vendor信息
     */
    private String processStreamResponseWithVendorAndId(String message, ChatContext chatContext) {
        try {
            var jsonNode = JsonUtils.parseJson(message);
            if (jsonNode.has("response")) {
                var responseNode = jsonNode.path("response");

                // 创建可修改的JSON对象
                var objectNode = (com.fasterxml.jackson.databind.node.ObjectNode) jsonNode;
                var responseObjectNode = (com.fasterxml.jackson.databind.node.ObjectNode) responseNode;

                // 修改response中的ID，使用TraceUtils.traceId()
                if (responseNode.has("id")) {
                    String originalId = responseNode.path("id").asText();
                    String newId = "your-" + TraceUtils.traceId();
                    responseObjectNode.put("id", newId);
                    log.debug("修改Response ID | 原ID: {} | 新ID: {}", originalId, newId);
                }

                // 注入vendor信息到response中，使用mapVendorName方法
                BestKeyResponse keyInfo = chatContext.getKeyInfo();
                if (keyInfo != null) {
                    String mappedVendor = mapVendorName(keyInfo.getChannel());
                    responseObjectNode.put("vendor", mappedVendor);
                    log.debug("注入vendor信息到response | vendor: {}", mappedVendor);
                }

                return JsonUtils.toJSONString(objectNode);
            }
            return message;
        } catch (Exception e) {
            log.warn("处理流式Response vendor和ID注入失败，返回原始消息 | 错误: {}", e.getMessage());
            return message;
        }
    }

}
